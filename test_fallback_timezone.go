package main

import (
	"fmt"
	"time"

	"github.com/ducminhgd/go-atlassian/internal/msteams"
)

func main() {
	// Test with invalid timezone to trigger fallback
	reportDate := time.Now()
	
	// Create minimal test data
	epicGroups := map[string]*msteams.EpicGroup{}
	noEpicIssues := []msteams.IssueUpdate{}
	subtitleConfig := msteams.SubtitleConfig{
		QueryType:     "test",
		JiraProject:   "TEST",
		LookbackHours: 24,
		JiraHost:      "test.example.com",
	}

	// Test with invalid timezone - should fallback to UTC+7
	fmt.Println("Testing with invalid timezone 'Invalid/Timezone'...")
	card := msteams.FormatJiraReportAsAdaptiveCard(
		epicGroups, 
		noEpicIssues, 
		reportDate, 
		"Invalid/Timezone", // This will cause an error
		subtitleConfig,
	)

	// Check if the card was created successfully
	if len(card.Body) > 0 {
		fmt.Println("✅ Card created successfully with fallback timezone")
		
		// Look for the title to see the date format
		for _, element := range card.Body {
			if textBlock, ok := element.(map[string]interface{}); ok {
				if text, exists := textBlock["text"]; exists {
					if textStr, isString := text.(string); isString {
						fmt.Printf("Title: %s\n", textStr)
						break
					}
				}
			}
		}
	} else {
		fmt.Println("❌ Failed to create card")
	}

	// Test with valid timezone for comparison
	fmt.Println("\nTesting with valid timezone 'Asia/Bangkok'...")
	card2 := msteams.FormatJiraReportAsAdaptiveCard(
		epicGroups, 
		noEpicIssues, 
		reportDate, 
		"Asia/Bangkok",
		subtitleConfig,
	)

	if len(card2.Body) > 0 {
		fmt.Println("✅ Card created successfully with valid timezone")
		
		// Look for the title to see the date format
		for _, element := range card2.Body {
			if textBlock, ok := element.(map[string]interface{}); ok {
				if text, exists := textBlock["text"]; exists {
					if textStr, isString := text.(string); isString {
						fmt.Printf("Title: %s\n", textStr)
						break
					}
				}
			}
		}
	} else {
		fmt.Println("❌ Failed to create card")
	}

	// Test the timezone creation directly
	fmt.Println("\nTesting timezone creation directly...")
	
	// Test invalid timezone
	_, err := time.LoadLocation("Invalid/Timezone")
	if err != nil {
		fallbackLoc := time.FixedZone("UTC+7", 7*3600)
		now := time.Now().In(fallbackLoc)
		fmt.Printf("Fallback timezone UTC+7: %s\n", now.Format("2006-01-02 15:04:05 MST"))
	}
	
	// Test valid timezone
	validLoc, err := time.LoadLocation("Asia/Bangkok")
	if err == nil {
		now := time.Now().In(validLoc)
		fmt.Printf("Valid timezone Asia/Bangkok: %s\n", now.Format("2006-01-02 15:04:05 MST"))
	}
}
